{"name": "blockchain-service", "version": "1.0.0", "description": "DL引擎区块链微服务 - 处理NFT、智能合约和数字资产管理", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^9.4.3", "@nestjs/core": "^9.4.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/typeorm": "^9.0.1", "@nestjs/config": "^2.3.4", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/swagger": "^6.3.0", "@nestjs/microservices": "^9.4.3", "@nestjs/bull": "^10.0.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "bull": "^4.11.3", "ethers": "^6.7.1", "web3": "^4.1.1", "ipfs-http-client": "^60.0.1", "axios": "^1.5.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "bcrypt": "^5.1.0", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "express-session": "^1.17.3"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcrypt": "^5.0.0", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@types/express-session": "^1.17.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}