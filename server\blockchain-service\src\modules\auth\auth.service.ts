/**
 * 认证服务
 */

import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { User } from '../../entities/user.entity';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
  ) {}

  /**
   * 用户注册
   */
  async register(userData: Partial<User>) {
    const { email, password, ...rest } = userData;
    
    // 检查用户是否已存在
    const existingUser = await this.userRepository.findOne({ where: { email } });
    if (existingUser) {
      throw new Error('User already exists');
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 10);

    // 创建用户
    const user = this.userRepository.create({
      ...rest,
      email,
      passwordHash,
    });

    const savedUser = await this.userRepository.save(user);
    
    // 生成JWT令牌
    const token = this.generateToken(savedUser);

    return {
      user: this.sanitizeUser(savedUser),
      token,
    };
  }

  /**
   * 用户登录
   */
  async login(email: string, password: string) {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // 更新最后登录时间
    await this.userRepository.update(user.id, {
      lastLoginAt: new Date(),
    });

    const token = this.generateToken(user);

    return {
      user: this.sanitizeUser(user),
      token,
    };
  }

  /**
   * 钱包登录
   */
  async loginWithWallet(walletAddress: string, signature: string, message: string) {
    // 这里应该验证签名
    // 简化实现，实际应该验证钱包签名
    
    let user = await this.userRepository.findOne({ where: { walletAddress } });
    
    if (!user) {
      // 如果用户不存在，创建新用户
      user = this.userRepository.create({
        walletAddress,
        username: `user_${walletAddress.slice(-8)}`,
        email: `${walletAddress}@wallet.local`,
      });
      user = await this.userRepository.save(user);
    }

    const token = this.generateToken(user);

    return {
      user: this.sanitizeUser(user),
      token,
    };
  }

  /**
   * 生成JWT令牌
   */
  private generateToken(user: User) {
    const payload = {
      sub: user.id,
      email: user.email,
      walletAddress: user.walletAddress,
      role: user.role,
    };

    return this.jwtService.sign(payload);
  }

  /**
   * 清理用户敏感信息
   */
  private sanitizeUser(user: User) {
    const { passwordHash, ...sanitized } = user;
    return sanitized;
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    return user ? this.sanitizeUser(user) : null;
  }
}
